import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { <PERSON>, CardHeader, CardT<PERSON>le, CardContent, CardFooter } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';

const formSchema = z.object({
  companyName: z.string().min(2, "Company name is required"),
  email: z.string().email("Please enter a valid email address"),
  password: z.string().min(8, "Password must be at least 8 characters"),
});

type FormData = z.infer<typeof formSchema>;

export function RegisterPage() {
  const [step, setStep] = useState(1);
  
  const { register, handleSubmit, formState: { errors, isSubmitting } } = useForm<FormData>({
    resolver: zodResolver(formSchema),
  });
  
  const onSubmit = (data: FormData) => {
    console.log('Registration form submitted:', data);
    // Here you would typically handle registration
  };
  
  return (
    <Card className="w-full">
      <CardHeader className="space-y-1">
        <CardTitle className="text-2xl font-bold">Create an account</CardTitle>
        <p className="text-sm text-gray-500">
          Enter your details to create your Flawagram company account
        </p>
      </CardHeader>
      
      <form onSubmit={handleSubmit(onSubmit)}>
        <CardContent className="space-y-4">
          {step === 1 && (
            <>
              <div className="space-y-2">
                <label htmlFor="companyName\" className="text-sm font-medium">
                  Company Name
                </label>
                <Input
                  id="companyName"
                  placeholder="Acme Inc."
                  {...register("companyName")}
                />
                {errors.companyName && (
                  <p className="text-red-500 text-xs">{errors.companyName.message}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <label htmlFor="email" className="text-sm font-medium">
                  Email
                </label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  {...register("email")}
                />
                {errors.email && (
                  <p className="text-red-500 text-xs">{errors.email.message}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <label htmlFor="password" className="text-sm font-medium">
                  Password
                </label>
                <Input
                  id="password"
                  type="password"
                  {...register("password")}
                />
                {errors.password && (
                  <p className="text-red-500 text-xs">{errors.password.message}</p>
                )}
              </div>
              
              <p className="text-xs text-gray-500">
                By clicking continue, you agree to our{' '}
                <Link to="/terms" className="text-black hover:underline">
                  Terms of Service
                </Link>{' '}
                and{' '}
                <Link to="/privacy" className="text-black hover:underline">
                  Privacy Policy
                </Link>
                .
              </p>
            </>
          )}
          
          {step === 2 && (
            <div className="space-y-4">
              <div className="text-center py-4">
                <div className="inline-flex items-center justify-center p-2 bg-gray-100 rounded-full mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6">
                    <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
                  </svg>
                </div>
                <h3 className="font-semibold text-lg mb-2">Verify your email</h3>
                <p className="text-gray-500 text-sm">
                  We've sent a verification link to your email address. Please check your inbox and click the link to continue.
                </p>
              </div>
              
              <div className="text-center">
                <p className="text-xs text-gray-500 mb-2">
                  Didn't receive an email?
                </p>
                <Button type="button" variant="outline" size="sm">
                  Resend Verification Email
                </Button>
              </div>
            </div>
          )}
        </CardContent>
        
        <CardFooter className="flex flex-col space-y-4">
          {step === 1 ? (
            <>
              <Button 
                type="button" 
                className="w-full"
                onClick={() => setStep(2)}
              >
                Continue
              </Button>
              
              <p className="text-sm text-center text-gray-500">
                Already have an account?{' '}
                <Link to="/login" className="text-black hover:underline">
                  Log in
                </Link>
              </p>
            </>
          ) : (
            <Link to="/login" className="text-sm text-center text-gray-500 hover:underline">
              Back to login
            </Link>
          )}
        </CardFooter>
      </form>
    </Card>
  );
}