import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Card, CardHeader, CardT<PERSON>le, CardContent, CardFooter } from '../ui/Card';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Textarea } from '../ui/Textarea';
import { FlawagramFormData, Company } from '../../types';
import { SearchIcon, PlusIcon, MinusIcon, CheckIcon } from 'lucide-react';
import { useData } from '../../contexts/DataContext';

const formSchema = z.object({
  recipientCompanyId: z.string().min(1, "Recipient is required"),
  message: z.string().min(10, "Message must be at least 10 characters"),
  giftId: z.string().optional(),
  scheduledDate: z.date().optional(),
});

interface FlawagramFormProps {
  onSubmit: (data: FlawagramFormData) => void;
  isSubmitting?: boolean;
}

export function FlawagramForm({ onSubmit, isSubmitting = false }: FlawagramFormProps) {
  const [showGiftOptions, setShowGiftOptions] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCompany, setSelectedCompany] = useState<Company | null>(null);
  const [showCompanyResults, setShowCompanyResults] = useState(false);
  const { companies, gifts, searchCompanies } = useData();

  const searchResults = searchCompanies(searchQuery);
  
  const { register, handleSubmit, formState: { errors }, setValue } = useForm<FlawagramFormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      message: '',
    }
  });

  const handleFormSubmit = (data: FlawagramFormData) => {
    onSubmit(data);
  };

  const handleCompanySelect = (company: Company) => {
    setSelectedCompany(company);
    setSearchQuery(company.name);
    setShowCompanyResults(false);
    setValue('recipientCompanyId', company.id);
  };
  
  return (
    <Card className="max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Send a Flawagram</CardTitle>
      </CardHeader>
      
      <form onSubmit={handleSubmit(handleFormSubmit)}>
        <CardContent className="space-y-6">
          {/* Recipient Search */}
          <div className="space-y-2">
            <label htmlFor="recipient" className="block text-sm font-medium">
              Recipient Company
            </label>
            <div className="relative">
              <Input
                id="recipient"
                placeholder="Search for a company..."
                className="pl-9"
                value={searchQuery}
                onChange={(e) => {
                  setSearchQuery(e.target.value);
                  setShowCompanyResults(true);
                }}
                onFocus={() => setShowCompanyResults(true)}
              />
              <SearchIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />

              {/* Company Search Results */}
              {showCompanyResults && searchQuery && (
                <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-auto">
                  {searchResults.length > 0 ? (
                    searchResults.map((company) => (
                      <button
                        key={company.id}
                        type="button"
                        className="w-full px-4 py-3 text-left hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
                        onClick={() => handleCompanySelect(company)}
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-gray-900">{company.name}</p>
                            {company.industry && (
                              <p className="text-sm text-gray-500">{company.industry}</p>
                            )}
                          </div>
                          {selectedCompany?.id === company.id && (
                            <CheckIcon className="h-4 w-4 text-green-600" />
                          )}
                        </div>
                      </button>
                    ))
                  ) : (
                    <div className="px-4 py-3 text-gray-500 text-sm">
                      No companies found matching "{searchQuery}"
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Hidden input for form validation */}
            <input type="hidden" {...register("recipientCompanyId")} />

            {errors.recipientCompanyId && (
              <p className="text-red-500 text-xs mt-1">{errors.recipientCompanyId.message}</p>
            )}
          </div>
          
          {/* Message */}
          <div className="space-y-2">
            <label htmlFor="message" className="block text-sm font-medium">
              Your Message
            </label>
            <Textarea
              id="message"
              placeholder="Write your appreciation message here..."
              rows={5}
              {...register("message")}
            />
            {errors.message && (
              <p className="text-red-500 text-xs mt-1">{errors.message.message}</p>
            )}
          </div>
          
          {/* Gift Option */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <label className="block text-sm font-medium">
                Include a Gift
              </label>
              <Button 
                type="button"
                variant="outline" 
                size="sm"
                onClick={() => setShowGiftOptions(!showGiftOptions)}
              >
                {showGiftOptions ? 'Hide Options' : 'Show Options'}
              </Button>
            </div>
            
            {showGiftOptions && (
              <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
                <div className="space-y-2">
                  <label className="block text-sm font-medium">
                    Choose a Gift
                  </label>
                  <div className="grid grid-cols-1 gap-3">
                    {gifts.map((gift) => (
                      <label
                        key={gift.id}
                        className="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50"
                      >
                        <input
                          type="radio"
                          value={gift.id}
                          {...register("giftId")}
                          className="mr-3"
                        />
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <p className="text-sm font-medium">{gift.name}</p>
                            <span className="text-sm font-medium text-gray-900">${gift.price}</span>
                          </div>
                          <p className="text-xs text-gray-500 mt-1">{gift.description}</p>
                        </div>
                      </label>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
        
        <CardFooter className="flex justify-end gap-4 border-t pt-6">
          <Button type="button" variant="outline">
            Save as Draft
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? 'Sending...' : 'Send Flawagram'}
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
}