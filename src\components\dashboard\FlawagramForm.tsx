import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Card, CardHeader, CardT<PERSON>le, CardContent, CardFooter } from '../ui/Card';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Textarea } from '../ui/Textarea';
import { FlawagramFormData } from '../../types';
import { SearchIcon, PlusIcon, MinusIcon } from 'lucide-react';

const formSchema = z.object({
  recipientCompanyId: z.string().min(1, "Recipient is required"),
  message: z.string().min(10, "Message must be at least 10 characters"),
  giftId: z.string().optional(),
  scheduledDate: z.date().optional(),
});

interface FlawagramFormProps {
  onSubmit: (data: FlawagramFormData) => void;
}

export function FlawagramForm({ onSubmit }: FlawagramFormProps) {
  const [showGiftOptions, setShowGiftOptions] = useState(false);
  const [giftAmount, setGiftAmount] = useState(5);
  
  const { register, handleSubmit, formState: { errors, isSubmitting } } = useForm<FlawagramFormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      message: '',
    }
  });
  
  const handleFormSubmit = (data: FlawagramFormData) => {
    onSubmit(data);
  };
  
  return (
    <Card className="max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Send a Flawagram</CardTitle>
      </CardHeader>
      
      <form onSubmit={handleSubmit(handleFormSubmit)}>
        <CardContent className="space-y-6">
          {/* Recipient Search */}
          <div className="space-y-2">
            <label htmlFor="recipient" className="block text-sm font-medium">
              Recipient Company
            </label>
            <div className="relative">
              <Input
                id="recipient"
                placeholder="Search for a company..."
                className="pl-9"
                {...register("recipientCompanyId")}
              />
              <SearchIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
            </div>
            {errors.recipientCompanyId && (
              <p className="text-red-500 text-xs mt-1">{errors.recipientCompanyId.message}</p>
            )}
          </div>
          
          {/* Message */}
          <div className="space-y-2">
            <label htmlFor="message" className="block text-sm font-medium">
              Your Message
            </label>
            <Textarea
              id="message"
              placeholder="Write your appreciation message here..."
              rows={5}
              {...register("message")}
            />
            {errors.message && (
              <p className="text-red-500 text-xs mt-1">{errors.message.message}</p>
            )}
          </div>
          
          {/* Gift Option */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <label className="block text-sm font-medium">
                Include a Gift
              </label>
              <Button 
                type="button"
                variant="outline" 
                size="sm"
                onClick={() => setShowGiftOptions(!showGiftOptions)}
              >
                {showGiftOptions ? 'Hide Options' : 'Show Options'}
              </Button>
            </div>
            
            {showGiftOptions && (
              <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
                <div className="space-y-2">
                  <label className="block text-sm font-medium">
                    Gift Amount
                  </label>
                  <div className="flex items-center">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setGiftAmount(Math.max(5, giftAmount - 5))}
                      className="rounded-r-none"
                    >
                      <MinusIcon className="h-4 w-4" />
                    </Button>
                    <div className="px-4 py-2 border-y border-gray-200 text-center">
                      ${giftAmount}
                    </div>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setGiftAmount(giftAmount + 5)}
                      className="rounded-l-none"
                    >
                      <PlusIcon className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <label className="block text-sm font-medium">
                    Gift Options
                  </label>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="border border-gray-200 rounded p-2 cursor-pointer hover:bg-gray-100">
                      <p className="text-sm font-medium">Digital Gift Card</p>
                      <p className="text-xs text-gray-500">Delivered instantly</p>
                    </div>
                    <div className="border border-gray-200 rounded p-2 cursor-pointer hover:bg-gray-100">
                      <p className="text-sm font-medium">Donation</p>
                      <p className="text-xs text-gray-500">Give to a cause</p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
        
        <CardFooter className="flex justify-end gap-4 border-t pt-6">
          <Button type="button" variant="outline">
            Save as Draft
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? 'Sending...' : 'Send Flawagram'}
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
}