import React, { useState } from 'react';
import { Button } from '../components/ui/Button';
import { Card, CardHeader, CardT<PERSON>le, CardContent, CardFooter } from '../components/ui/Card';
import { Check, Star, Users, Building, Zap } from 'lucide-react';

export function PricingPage() {
  const [isAnnual, setIsAnnual] = useState(false);

  const plans = [
    {
      name: 'Starter',
      icon: Users,
      description: 'Perfect for small teams getting started',
      monthlyPrice: 9,
      annualPrice: 7,
      features: [
        'Up to 10 team members',
        'Unlimited Flawagrams',
        'Basic analytics',
        'Email support',
        'Standard templates',
        'Team dashboard'
      ],
      popular: false,
      cta: 'Start Free Trial'
    },
    {
      name: 'Professional',
      icon: Building,
      description: 'For growing teams that need more features',
      monthlyPrice: 19,
      annualPrice: 15,
      features: [
        'Up to 50 team members',
        'Unlimited Flawagrams',
        'Advanced analytics',
        'Priority support',
        'Custom templates',
        'Team insights',
        'Anonymous feedback',
        'Integration support'
      ],
      popular: true,
      cta: 'Start Free Trial'
    },
    {
      name: 'Enterprise',
      icon: Zap,
      description: 'For large organizations with advanced needs',
      monthlyPrice: 39,
      annualPrice: 31,
      features: [
        'Unlimited team members',
        'Unlimited Flawagrams',
        'Enterprise analytics',
        'Dedicated support',
        'Custom branding',
        'Advanced integrations',
        'SSO authentication',
        'Custom workflows',
        'API access',
        'Compliance features'
      ],
      popular: false,
      cta: 'Contact Sales'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 sm:text-5xl md:text-6xl">
              Simple, Transparent <span className="text-blue-600">Pricing</span>
            </h1>
            <p className="mt-6 max-w-3xl mx-auto text-xl text-gray-500">
              Choose the perfect plan for your team. Start with a free trial, 
              no credit card required.
            </p>
            
            {/* Billing Toggle */}
            <div className="mt-8 flex items-center justify-center">
              <span className={`text-sm ${!isAnnual ? 'text-gray-900 font-medium' : 'text-gray-500'}`}>
                Monthly
              </span>
              <button
                onClick={() => setIsAnnual(!isAnnual)}
                className={`mx-3 relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                  isAnnual ? 'bg-blue-600' : 'bg-gray-200'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    isAnnual ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
              <span className={`text-sm ${isAnnual ? 'text-gray-900 font-medium' : 'text-gray-500'}`}>
                Annual
              </span>
              {isAnnual && (
                <span className="ml-2 inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                  Save 20%
                </span>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Cards */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid gap-8 lg:grid-cols-3">
            {plans.map((plan) => {
              const Icon = plan.icon;
              const price = isAnnual ? plan.annualPrice : plan.monthlyPrice;
              
              return (
                <Card 
                  key={plan.name} 
                  className={`relative ${
                    plan.popular 
                      ? 'border-blue-500 shadow-lg scale-105' 
                      : 'border-gray-200'
                  }`}
                >
                  {plan.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <div className="inline-flex items-center rounded-full bg-blue-600 px-4 py-1 text-sm font-medium text-white">
                        <Star className="h-4 w-4 mr-1" />
                        Most Popular
                      </div>
                    </div>
                  )}
                  
                  <CardHeader className="text-center pb-8">
                    <Icon className={`h-12 w-12 mx-auto ${
                      plan.popular ? 'text-blue-600' : 'text-gray-400'
                    }`} />
                    <CardTitle className="mt-4 text-2xl font-bold">{plan.name}</CardTitle>
                    <p className="mt-2 text-gray-500">{plan.description}</p>
                    
                    <div className="mt-6">
                      <div className="flex items-center justify-center">
                        <span className="text-4xl font-bold text-gray-900">${price}</span>
                        <span className="text-gray-500 ml-2">
                          /user/{isAnnual ? 'month' : 'month'}
                        </span>
                      </div>
                      {isAnnual && (
                        <p className="text-sm text-gray-500 mt-1">
                          Billed annually (${price * 12}/user/year)
                        </p>
                      )}
                    </div>
                  </CardHeader>
                  
                  <CardContent>
                    <ul className="space-y-3">
                      {plan.features.map((feature, index) => (
                        <li key={index} className="flex items-start">
                          <Check className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
                          <span className="ml-3 text-gray-600">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                  
                  <CardFooter>
                    <Button 
                      className={`w-full ${
                        plan.popular 
                          ? 'bg-blue-600 hover:bg-blue-700 text-white' 
                          : 'bg-gray-100 hover:bg-gray-200 text-gray-900'
                      }`}
                      size="lg"
                    >
                      {plan.cta}
                    </Button>
                  </CardFooter>
                </Card>
              );
            })}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="bg-white py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">
              Frequently Asked Questions
            </h2>
          </div>
          
          <div className="space-y-8">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Can I try Flawa before purchasing?
              </h3>
              <p className="text-gray-600">
                Yes! We offer a 14-day free trial for all plans. No credit card required. 
                You can explore all features and see how Flawa works for your team.
              </p>
            </div>
            
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Can I change plans later?
              </h3>
              <p className="text-gray-600">
                Absolutely. You can upgrade or downgrade your plan at any time. 
                Changes take effect immediately, and we'll prorate any billing differences.
              </p>
            </div>
            
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                What payment methods do you accept?
              </h3>
              <p className="text-gray-600">
                We accept all major credit cards (Visa, MasterCard, American Express) 
                and offer invoice billing for Enterprise customers.
              </p>
            </div>
            
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Is there a setup fee?
              </h3>
              <p className="text-gray-600">
                No setup fees, ever. You only pay for your subscription, and you can 
                cancel at any time without penalties.
              </p>
            </div>
            
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Do you offer discounts for nonprofits or educational institutions?
              </h3>
              <p className="text-gray-600">
                Yes! We offer special pricing for qualified nonprofits and educational 
                institutions. Contact our sales team for more information.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-blue-600 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white sm:text-4xl">
            Ready to Transform Your Team Communication?
          </h2>
          <p className="mt-4 text-xl text-blue-100">
            Start your free trial today. No credit card required.
          </p>
          <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
              Start Free Trial
            </Button>
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600">
              Contact Sales
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
