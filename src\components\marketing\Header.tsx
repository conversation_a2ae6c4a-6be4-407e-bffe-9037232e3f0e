import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Button } from '../ui/Button';
import { FlowerIcon, MenuIcon, XIcon } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';

export function Header() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { user } = useAuth();
  
  return (
    <header className="sticky top-0 z-50 w-full border-b bg-white">
      <div className="container mx-auto flex h-16 items-center justify-between px-4">
        <Link to="/" className="flex items-center">
          <FlowerIcon className="h-6 w-6 mr-2" />
          <span className="text-xl font-semibold">Flawagram</span>
        </Link>
        
        {/* Desktop navigation */}
        <nav className="hidden md:flex items-center gap-6">
          <Link to="/how-it-works" className="text-sm font-medium text-gray-700 hover:text-black">
            How It Works
          </Link>
          <Link to="/pricing" className="text-sm font-medium text-gray-700 hover:text-black">
            Pricing
          </Link>
          <Link to="/about" className="text-sm font-medium text-gray-700 hover:text-black">
            About
          </Link>
        </nav>
        
        <div className="hidden md:flex items-center gap-4">
          {user ? (
            <Link to="/dashboard">
              <Button>Dashboard</Button>
            </Link>
          ) : (
            <>
              <Link to="/login">
                <Button variant="outline">Log in</Button>
              </Link>
              <Link to="/register">
                <Button>Get Started</Button>
              </Link>
            </>
          )}
        </div>
        
        {/* Mobile menu button */}
        <button
          className="rounded-md p-2 md:hidden"
          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
        >
          {mobileMenuOpen ? (
            <XIcon className="h-6 w-6" />
          ) : (
            <MenuIcon className="h-6 w-6" />
          )}
        </button>
      </div>
      
      {/* Mobile navigation */}
      {mobileMenuOpen && (
        <nav className="border-t py-4 px-4 md:hidden">
          <div className="flex flex-col gap-4">
            <Link to="/how-it-works" className="text-sm font-medium text-gray-700">
              How It Works
            </Link>
            <Link to="/pricing" className="text-sm font-medium text-gray-700">
              Pricing
            </Link>
            <Link to="/about" className="text-sm font-medium text-gray-700">
              About
            </Link>
            <hr className="my-2" />
            {user ? (
              <Link to="/dashboard">
                <Button className="w-full">Dashboard</Button>
              </Link>
            ) : (
              <>
                <Link to="/login" className="text-sm font-medium text-gray-700">
                  Log in
                </Link>
                <Link to="/register">
                  <Button className="w-full">Get Started</Button>
                </Link>
              </>
            )}
          </div>
        </nav>
      )}
    </header>
  );
}