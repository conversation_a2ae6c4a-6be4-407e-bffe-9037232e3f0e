import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Card, CardContent } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Textarea } from '../components/ui/Textarea';
import { 
  FlowerIcon, 
  CheckCircleIcon, 
  HeartIcon, 
  SendIcon,
  ArrowRightIcon
} from 'lucide-react';

export function FlawagramPage() {
  const [showResponse, setShowResponse] = useState(false);
  
  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="container mx-auto max-w-md">
        <Link to="/" className="inline-flex items-center mb-8">
          <FlowerIcon className="h-6 w-6 mr-2" />
          <span className="text-xl font-semibold">Flawagram</span>
        </Link>
        
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card className="shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center">
                  <div className="w-12 h-12 rounded-full bg-gray-200 mr-3 flex items-center justify-center">
                    <FlowerIcon className="h-6 w-6" />
                  </div>
                  <div>
                    <h3 className="font-semibold">TechCorp</h3>
                    <p className="text-xs text-gray-500">May 15, 2025</p>
                  </div>
                </div>
                <div className="bg-black text-white text-xs px-2 py-1 rounded">
                  Flawagram
                </div>
              </div>
              
              <div className="border-l-4 border-gray-200 pl-4 mb-6">
                <p className="text-gray-700 italic">
                  "Thank you for the incredible partnership over the past quarter. Your team's dedication and creativity have made a significant impact on our project's success!"
                </p>
              </div>
              
              <div className="bg-gray-50 p-4 rounded-lg mb-6">
                <div className="flex items-center justify-between mb-2">
                  <p className="font-medium">Digital Gift Card</p>
                  <p className="font-semibold">$50</p>
                </div>
                <p className="text-xs text-gray-500">Redeemable at recipient's choice of stores</p>
              </div>
              
              {!showResponse ? (
                <div className="space-y-4">
                  <Button 
                    className="w-full" 
                    onClick={() => setShowResponse(true)}
                  >
                    <CheckCircleIcon className="h-4 w-4 mr-2" />
                    Accept Gift & Respond
                  </Button>
                  
                  <div className="text-center">
                    <Button variant="ghost" className="text-sm">
                      <HeartIcon className="h-4 w-4 mr-2" />
                      Just Say Thanks
                    </Button>
                  </div>
                </div>
              ) : (
                <motion.div 
                  className="space-y-4"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.3 }}
                >
                  <p className="text-sm font-medium mb-2">Your Response</p>
                  <Textarea 
                    placeholder="Write your response here..."
                    className="w-full"
                    rows={4}
                  />
                  
                  <div className="flex gap-3">
                    <Button className="flex-1">
                      <SendIcon className="h-4 w-4 mr-2" />
                      Send Response
                    </Button>
                    <Button variant="outline" className="flex-1">
                      Create Account
                      <ArrowRightIcon className="h-4 w-4 ml-2" />
                    </Button>
                  </div>
                </motion.div>
              )}
            </CardContent>
          </Card>
          
          <div className="mt-8 text-center">
            <p className="text-sm text-gray-500 mb-4">
              Want to send your own Flawagrams?
            </p>
            <Link to="/register">
              <Button variant="outline">
                Create Your Account
                <ArrowRightIcon className="h-4 w-4 ml-2" />
              </Button>
            </Link>
          </div>
        </motion.div>
      </div>
    </div>
  );
}