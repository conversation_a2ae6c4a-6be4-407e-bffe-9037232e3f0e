import React, { createContext, useContext, useState, useEffect } from 'react';
import { Flawagram, Company, Gift, FlawagramFormData } from '../types';
import { useAuth } from './AuthContext';

interface DataContextType {
  flawagrams: Flawagram[];
  companies: Company[];
  gifts: Gift[];
  isLoading: boolean;
  sendFlawagram: (data: FlawagramFormData) => Promise<void>;
  getFlawagramById: (id: string) => Flawagram | undefined;
  searchCompanies: (query: string) => Company[];
  refreshData: () => Promise<void>;
}

const DataContext = createContext<DataContextType | undefined>(undefined);

export function useData() {
  const context = useContext(DataContext);
  if (context === undefined) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
}

// Mock data
const mockCompanies: Company[] = [
  {
    id: 'company1',
    name: 'TechCorp Solutions',
    logoUrl: '',
    industry: 'Technology',
    size: '50-100',
    createdAt: new Date('2024-01-15'),
  },
  {
    id: 'company2',
    name: 'AcmeCo Industries',
    logoUrl: '',
    industry: 'Manufacturing',
    size: '100-500',
    createdAt: new Date('2024-02-01'),
  },
  {
    id: 'company3',
    name: 'Creative Studio',
    logoUrl: '',
    industry: 'Design',
    size: '10-50',
    createdAt: new Date('2024-01-20'),
  },
  {
    id: 'company4',
    name: 'Global Consulting',
    logoUrl: '',
    industry: 'Consulting',
    size: '500+',
    createdAt: new Date('2024-01-10'),
  },
];

const mockGifts: Gift[] = [
  {
    id: 'gift1',
    name: 'Digital Gift Card',
    description: 'A versatile digital gift card',
    imageUrl: '',
    price: 25,
    category: 'digital',
    isAvailable: true,
  },
  {
    id: 'gift2',
    name: 'Coffee Shop Voucher',
    description: 'Perfect for coffee lovers',
    imageUrl: '',
    price: 15,
    category: 'digital',
    isAvailable: true,
  },
  {
    id: 'gift3',
    name: 'Charity Donation',
    description: 'Make a donation in their name',
    imageUrl: '',
    price: 50,
    category: 'donation',
    isAvailable: true,
  },
];

const mockFlawagrams: Flawagram[] = [
  {
    id: 'flawagram1',
    message: 'Thank you for your excellent work on the recent project. Your team\'s attention to detail was outstanding!',
    senderId: 'user1',
    senderCompanyId: 'company1',
    senderCompany: mockCompanies[0],
    recipientId: 'user2',
    recipientCompanyId: 'company2',
    recipientCompany: mockCompanies[1],
    status: 'delivered',
    createdAt: new Date('2024-01-20'),
    gift: mockGifts[0],
    giftId: 'gift1',
  },
  {
    id: 'flawagram2',
    message: 'We really appreciate the partnership and look forward to working together on future projects.',
    senderId: 'user2',
    senderCompanyId: 'company2',
    senderCompany: mockCompanies[1],
    recipientId: 'user1',
    recipientCompanyId: 'company1',
    recipientCompany: mockCompanies[0],
    status: 'viewed',
    createdAt: new Date('2024-01-22'),
  },
];

export function DataProvider({ children }: { children: React.ReactNode }) {
  const [flawagrams, setFlawagrams] = useState<Flawagram[]>([]);
  const [companies, setCompanies] = useState<Company[]>([]);
  const [gifts, setGifts] = useState<Gift[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { user, company } = useAuth();

  useEffect(() => {
    // Load initial data
    loadData();
  }, [user]);

  const loadData = async () => {
    setIsLoading(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    
    setCompanies(mockCompanies);
    setGifts(mockGifts);
    setFlawagrams(mockFlawagrams);
    
    setIsLoading(false);
  };

  const sendFlawagram = async (data: FlawagramFormData) => {
    if (!user || !company) {
      throw new Error('User must be authenticated to send Flawagrams');
    }

    setIsLoading(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const recipientCompany = companies.find(c => c.id === data.recipientCompanyId);
    if (!recipientCompany) {
      throw new Error('Recipient company not found');
    }

    const gift = data.giftId ? gifts.find(g => g.id === data.giftId) : undefined;

    const newFlawagram: Flawagram = {
      id: `flawagram_${Date.now()}`,
      message: data.message,
      senderId: user.id,
      senderCompanyId: company.id,
      senderCompany: company,
      recipientCompanyId: data.recipientCompanyId,
      recipientCompany: recipientCompany,
      status: 'sent',
      createdAt: new Date(),
      giftId: data.giftId,
      gift,
    };

    setFlawagrams(prev => [newFlawagram, ...prev]);
    setIsLoading(false);
  };

  const getFlawagramById = (id: string) => {
    return flawagrams.find(f => f.id === id);
  };

  const searchCompanies = (query: string) => {
    if (!query.trim()) return companies;
    
    return companies.filter(company =>
      company.name.toLowerCase().includes(query.toLowerCase()) ||
      (company.industry && company.industry.toLowerCase().includes(query.toLowerCase()))
    );
  };

  const refreshData = async () => {
    await loadData();
  };

  const value = {
    flawagrams,
    companies,
    gifts,
    isLoading,
    sendFlawagram,
    getFlawagramById,
    searchCompanies,
    refreshData,
  };

  return (
    <DataContext.Provider value={value}>
      {children}
    </DataContext.Provider>
  );
}
