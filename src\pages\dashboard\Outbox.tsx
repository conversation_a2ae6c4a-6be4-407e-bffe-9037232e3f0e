import React, { useState } from 'react';
import { FlawagramCard } from '../../components/dashboard/FlawagramCard';
import { Card, CardHeader, CardTitle, CardContent } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { SearchIcon, FilterIcon } from 'lucide-react';
import { Flawagram } from '../../types';

// Mock data
const sentFlawagrams: Flawagram[] = [
  {
    id: '1',
    message: 'Thank you for being an exceptional partner throughout our recent project. Your team\'s expertise and dedication made a significant impact.',
    senderId: 'user2',
    senderCompanyId: 'company2',
    senderCompany: { 
      id: 'company2', 
      name: 'AcmeCo', 
      logoUrl: '', 
      createdAt: new Date() 
    },
    recipientId: 'user1',
    recipientCompanyId: 'company1',
    recipientCompany: { 
      id: 'company1', 
      name: '<PERSON><PERSON>or<PERSON>', 
      logoUrl: '', 
      createdAt: new Date() 
    },
    status: 'delivered',
    createdAt: new Date(),
    gift: {
      id: 'gift1',
      name: 'Digital Gift Card',
      description: 'A digital gift card for recipient',
      imageUrl: '',
      price: 50,
      category: 'digital',
      isAvailable: true
    }
  },
  {
    id: '2',
    message: 'We appreciate your outstanding service and commitment to excellence. Looking forward to our continued collaboration.',
    senderId: 'user2',
    senderCompanyId: 'company2',
    senderCompany: { 
      id: 'company2', 
      name: 'AcmeCo', 
      logoUrl: '', 
      createdAt: new Date() 
    },
    recipientId: 'user3',
    recipientCompanyId: 'company3',
    recipientCompany: { 
      id: 'company3', 
      name: 'GlobalBiz', 
      logoUrl: '', 
      createdAt: new Date() 
    },
    status: 'viewed',
    createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
    gift: null
  },
  {
    id: '3',
    message: 'Your team\'s innovative solutions have transformed our workflow. Thank you for your exceptional support.',
    senderId: 'user2',
    senderCompanyId: 'company2',
    senderCompany: { 
      id: 'company2', 
      name: 'AcmeCo', 
      logoUrl: '', 
      createdAt: new Date() 
    },
    recipientId: 'user4',
    recipientCompanyId: 'company4',
    recipientCompany: { 
      id: 'company4', 
      name: 'FutureTech', 
      logoUrl: '', 
      createdAt: new Date() 
    },
    status: 'responded',
    createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
    gift: {
      id: 'gift2',
      name: 'Donation',
      description: 'A donation to a cause',
      imageUrl: '',
      price: 25,
      category: 'donation',
      isAvailable: true
    }
  },
];

export function Outbox() {
  const [searchTerm, setSearchTerm] = useState('');
  
  const filteredFlawagrams = sentFlawagrams.filter(flawagram => 
    flawagram.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
    flawagram.recipientCompany.name.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">Outbox</h1>
        <p className="text-gray-500">Track and manage Flawagrams you've sent.</p>
      </div>
      
      <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
        <div className="relative w-full md:w-auto flex-1 max-w-sm">
          <SearchIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search sent Flawagrams..."
            className="pl-9"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        
        <div className="flex gap-2 w-full md:w-auto">
          <Button variant="outline" className="flex-1 md:flex-initial">
            <FilterIcon className="mr-2 h-4 w-4" />
            Filter
          </Button>
          <select className="h-10 rounded-md border border-gray-200 bg-white px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-black focus-visible:ring-offset-2">
            <option value="recent">Most Recent</option>
            <option value="oldest">Oldest First</option>
            <option value="unresponded">Awaiting Response</option>
          </select>
        </div>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle className="text-base font-medium">
            Your Sent Flawagrams
          </CardTitle>
        </CardHeader>
        <CardContent>
          {filteredFlawagrams.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {filteredFlawagrams.map(flawagram => (
                <FlawagramCard
                  key={flawagram.id}
                  flawagram={flawagram}
                  type="sent"
                  onView={(id) => console.log('View flawagram', id)}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-500">No Flawagrams found matching your search.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}