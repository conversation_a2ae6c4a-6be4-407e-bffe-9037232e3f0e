import React from 'react';
import { <PERSON><PERSON> } from '../components/ui/Button';
import { <PERSON>, Card<PERSON>eader, Card<PERSON><PERSON><PERSON>, CardContent } from '../components/ui/Card';
import { Send, Users, Eye, BarChart3, MessageSquare, CheckCircle } from 'lucide-react';

export function HowItWorksPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 sm:text-5xl md:text-6xl">
              How <span className="text-blue-600">Flawa</span> Works
            </h1>
            <p className="mt-6 max-w-3xl mx-auto text-xl text-gray-500">
              Transform your team's communication in three simple steps. 
              Send structured feedback, build transparency, and drive continuous improvement.
            </p>
          </div>
        </div>
      </section>

      {/* Steps Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">
              Simple, Powerful, Effective
            </h2>
            <p className="mt-4 text-lg text-gray-500">
              Get started with Flawagrams in minutes, not hours
            </p>
          </div>

          <div className="space-y-16">
            {/* Step 1 */}
            <div className="lg:grid lg:grid-cols-2 lg:gap-8 lg:items-center">
              <div>
                <div className="flex items-center">
                  <div className="flex-shrink-0 w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                    <span className="text-white font-bold text-lg">1</span>
                  </div>
                  <h3 className="ml-4 text-2xl font-bold text-gray-900">
                    Create Your Flawagram
                  </h3>
                </div>
                <p className="mt-4 text-lg text-gray-500">
                  Use our structured template to craft meaningful feedback. Choose from 
                  different categories like recognition, improvement suggestions, or general 
                  observations. Add context, specific examples, and actionable recommendations.
                </p>
                <ul className="mt-6 space-y-3">
                  <li className="flex items-start">
                    <CheckCircle className="flex-shrink-0 h-6 w-6 text-green-500 mt-0.5" />
                    <span className="ml-3 text-gray-600">Pre-built templates for different feedback types</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="flex-shrink-0 h-6 w-6 text-green-500 mt-0.5" />
                    <span className="ml-3 text-gray-600">Anonymous or attributed options</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="flex-shrink-0 h-6 w-6 text-green-500 mt-0.5" />
                    <span className="ml-3 text-gray-600">Rich text formatting and attachments</span>
                  </li>
                </ul>
              </div>
              <div className="mt-8 lg:mt-0">
                <Card className="bg-gradient-to-br from-blue-50 to-indigo-100 border-blue-200">
                  <CardHeader>
                    <div className="flex items-center">
                      <Send className="h-8 w-8 text-blue-600" />
                      <CardTitle className="ml-3 text-blue-900">Send Feedback</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="bg-white p-4 rounded-lg">
                        <p className="font-medium text-gray-900">Recognition</p>
                        <p className="text-sm text-gray-600 mt-1">
                          Great job on the presentation! Your clear explanations helped everyone understand the complex concepts.
                        </p>
                      </div>
                      <div className="bg-white p-4 rounded-lg">
                        <p className="font-medium text-gray-900">Suggestion</p>
                        <p className="text-sm text-gray-600 mt-1">
                          Consider adding more visual aids to make the data even more accessible.
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Step 2 */}
            <div className="lg:grid lg:grid-cols-2 lg:gap-8 lg:items-center">
              <div className="lg:order-2">
                <div className="flex items-center">
                  <div className="flex-shrink-0 w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                    <span className="text-white font-bold text-lg">2</span>
                  </div>
                  <h3 className="ml-4 text-2xl font-bold text-gray-900">
                    Share with Your Team
                  </h3>
                </div>
                <p className="mt-4 text-lg text-gray-500">
                  Send your Flawagram directly to individuals or share with the entire team. 
                  Control visibility settings to ensure the right people see the right feedback 
                  at the right time.
                </p>
                <ul className="mt-6 space-y-3">
                  <li className="flex items-start">
                    <CheckCircle className="flex-shrink-0 h-6 w-6 text-green-500 mt-0.5" />
                    <span className="ml-3 text-gray-600">Flexible sharing options</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="flex-shrink-0 h-6 w-6 text-green-500 mt-0.5" />
                    <span className="ml-3 text-gray-600">Team-wide or individual visibility</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="flex-shrink-0 h-6 w-6 text-green-500 mt-0.5" />
                    <span className="ml-3 text-gray-600">Real-time notifications</span>
                  </li>
                </ul>
              </div>
              <div className="mt-8 lg:mt-0 lg:order-1">
                <Card className="bg-gradient-to-br from-green-50 to-emerald-100 border-green-200">
                  <CardHeader>
                    <div className="flex items-center">
                      <Users className="h-8 w-8 text-green-600" />
                      <CardTitle className="ml-3 text-green-900">Team Visibility</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between bg-white p-3 rounded-lg">
                        <span className="text-gray-900">Sarah Johnson</span>
                        <Eye className="h-4 w-4 text-gray-400" />
                      </div>
                      <div className="flex items-center justify-between bg-white p-3 rounded-lg">
                        <span className="text-gray-900">Team Marketing</span>
                        <Eye className="h-4 w-4 text-gray-400" />
                      </div>
                      <div className="flex items-center justify-between bg-white p-3 rounded-lg">
                        <span className="text-gray-900">Anonymous</span>
                        <Eye className="h-4 w-4 text-gray-400" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Step 3 */}
            <div className="lg:grid lg:grid-cols-2 lg:gap-8 lg:items-center">
              <div>
                <div className="flex items-center">
                  <div className="flex-shrink-0 w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                    <span className="text-white font-bold text-lg">3</span>
                  </div>
                  <h3 className="ml-4 text-2xl font-bold text-gray-900">
                    Track Progress & Insights
                  </h3>
                </div>
                <p className="mt-4 text-lg text-gray-500">
                  Monitor feedback trends, track improvement over time, and gain valuable 
                  insights into team dynamics. Use our analytics dashboard to identify 
                  patterns and celebrate growth.
                </p>
                <ul className="mt-6 space-y-3">
                  <li className="flex items-start">
                    <CheckCircle className="flex-shrink-0 h-6 w-6 text-green-500 mt-0.5" />
                    <span className="ml-3 text-gray-600">Comprehensive analytics dashboard</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="flex-shrink-0 h-6 w-6 text-green-500 mt-0.5" />
                    <span className="ml-3 text-gray-600">Progress tracking over time</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="flex-shrink-0 h-6 w-6 text-green-500 mt-0.5" />
                    <span className="ml-3 text-gray-600">Team performance insights</span>
                  </li>
                </ul>
              </div>
              <div className="mt-8 lg:mt-0">
                <Card className="bg-gradient-to-br from-purple-50 to-violet-100 border-purple-200">
                  <CardHeader>
                    <div className="flex items-center">
                      <BarChart3 className="h-8 w-8 text-purple-600" />
                      <CardTitle className="ml-3 text-purple-900">Analytics</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="bg-white p-4 rounded-lg">
                        <div className="flex justify-between items-center">
                          <span className="text-gray-900 font-medium">Feedback Sent</span>
                          <span className="text-2xl font-bold text-purple-600">47</span>
                        </div>
                      </div>
                      <div className="bg-white p-4 rounded-lg">
                        <div className="flex justify-between items-center">
                          <span className="text-gray-900 font-medium">Team Engagement</span>
                          <span className="text-2xl font-bold text-green-600">94%</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="bg-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">
              Powerful Features for Modern Teams
            </h2>
          </div>
          
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader>
                <MessageSquare className="h-8 w-8 text-blue-600" />
                <CardTitle className="mt-4">Smart Templates</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-500">
                  Pre-built templates for different types of feedback ensure consistency 
                  and help users provide more structured, actionable input.
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <Eye className="h-8 w-8 text-blue-600" />
                <CardTitle className="mt-4">Privacy Controls</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-500">
                  Flexible privacy settings allow for anonymous feedback when needed, 
                  while maintaining transparency where appropriate.
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <BarChart3 className="h-8 w-8 text-blue-600" />
                <CardTitle className="mt-4">Real-time Analytics</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-500">
                  Track feedback trends, measure team engagement, and identify areas 
                  for improvement with comprehensive analytics.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-blue-600 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white sm:text-4xl">
            Ready to Get Started?
          </h2>
          <p className="mt-4 text-xl text-blue-100">
            Join thousands of teams already improving their communication with Flawa.
          </p>
          <div className="mt-8">
            <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
              Start Your Free Trial
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
