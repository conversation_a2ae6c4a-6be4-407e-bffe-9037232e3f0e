export interface User {
  id: string;
  name: string;
  email: string;
  avatarUrl?: string;
  role: 'admin' | 'member';
  companyId: string;
}

export interface Company {
  id: string;
  name: string;
  logoUrl?: string;
  industry?: string;
  size?: string;
  createdAt: Date;
  members?: User[];
}

export interface Gift {
  id: string;
  name: string;
  description: string;
  imageUrl: string;
  price: number;
  category: 'digital' | 'physical' | 'donation';
  isAvailable: boolean;
}

export interface Flawagram {
  id: string;
  message: string;
  giftId?: string;
  gift?: Gift;
  senderId: string;
  senderCompanyId: string;
  senderCompany: Company;
  recipientId?: string;
  recipientCompanyId: string;
  recipientCompany: Company;
  status: 'draft' | 'sent' | 'delivered' | 'viewed' | 'responded';
  createdAt: Date;
  deliveredAt?: Date;
  viewedAt?: Date;
}

export interface FlawagramResponse {
  id: string;
  flawagramId: string;
  message: string;
  senderId: string;
  senderCompanyId: string;
  createdAt: Date;
}

export interface FlawagramFormData {
  recipientCompanyId: string;
  message: string;
  giftId?: string;
  scheduledDate?: Date;
}