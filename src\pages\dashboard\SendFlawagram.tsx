import React from 'react';
import { FlawagramForm } from '../../components/dashboard/FlawagramForm';
import { FlawagramFormData } from '../../types';

export function SendFlawagram() {
  const handleSubmit = (data: FlawagramFormData) => {
    console.log('Form submitted:', data);
    // Here you would typically send the data to your backend
  };
  
  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">Send a Flawagram</h1>
        <p className="text-gray-500">Create a message of appreciation with an optional gift.</p>
      </div>
      
      <FlawagramForm onSubmit={handleSubmit} />
    </div>
  );
}