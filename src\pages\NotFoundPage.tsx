import React from 'react';
import { Button } from '../components/ui/Button';
import { Home, ArrowLeft, Search } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';

export function NotFoundPage() {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center items-center px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full text-center">
        {/* 404 Illustration */}
        <div className="mb-8">
          <div className="text-9xl font-bold text-blue-600 mb-4">404</div>
          <div className="w-24 h-1 bg-blue-600 mx-auto rounded-full"></div>
        </div>

        {/* Error Message */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Page Not Found
          </h1>
          <p className="text-lg text-gray-600 mb-6">
            Oops! The page you're looking for doesn't exist. It might have been moved, 
            deleted, or you entered the wrong URL.
          </p>
        </div>

        {/* Action Buttons */}
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              onClick={() => navigate(-1)}
              variant="outline"
              className="flex items-center justify-center"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Go Back
            </Button>
            
            <Link to="/">
              <Button className="flex items-center justify-center w-full sm:w-auto">
                <Home className="h-4 w-4 mr-2" />
                Go Home
              </Button>
            </Link>
          </div>
          
          <div className="mt-8">
            <p className="text-sm text-gray-500 mb-4">
              Looking for something specific?
            </p>
            <div className="flex flex-col sm:flex-row gap-2">
              <Link to="/about" className="text-blue-600 hover:text-blue-800 text-sm">
                About Us
              </Link>
              <span className="hidden sm:inline text-gray-300">|</span>
              <Link to="/how-it-works" className="text-blue-600 hover:text-blue-800 text-sm">
                How It Works
              </Link>
              <span className="hidden sm:inline text-gray-300">|</span>
              <Link to="/pricing" className="text-blue-600 hover:text-blue-800 text-sm">
                Pricing
              </Link>
              <span className="hidden sm:inline text-gray-300">|</span>
              <Link to="/login" className="text-blue-600 hover:text-blue-800 text-sm">
                Login
              </Link>
            </div>
          </div>
        </div>

        {/* Help Section */}
        <div className="mt-12 p-6 bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-center mb-4">
            <Search className="h-6 w-6 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Need Help?
          </h3>
          <p className="text-gray-600 text-sm mb-4">
            If you think this is an error or you need assistance, please contact our support team.
          </p>
          <Button variant="outline" size="sm" className="w-full">
            Contact Support
          </Button>
        </div>

        {/* Footer */}
        <div className="mt-8 text-xs text-gray-400">
          <p>Error Code: 404 - Page Not Found</p>
        </div>
      </div>
    </div>
  );
}
