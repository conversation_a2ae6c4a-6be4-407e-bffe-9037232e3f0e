import React, { useState } from 'react';
import { FlawagramCard } from '../../components/dashboard/FlawagramCard';
import { Card, CardHeader, CardTitle, CardContent } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { SearchIcon, FilterIcon } from 'lucide-react';
import { Flawagram } from '../../types';

// Mock data
const receivedFlawagrams: Flawagram[] = [
  {
    id: '1',
    message: 'Thank you for your excellent work on the recent project. Your team\'s attention to detail was outstanding!',
    senderId: 'user1',
    senderCompanyId: 'company1',
    senderCompany: { 
      id: 'company1', 
      name: 'TechCorp', 
      logoUrl: '', 
      createdAt: new Date() 
    },
    recipientId: 'user2',
    recipientCompanyId: 'company2',
    recipientCompany: { 
      id: 'company2', 
      name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', 
      logoUrl: '', 
      createdAt: new Date() 
    },
    status: 'delivered',
    createdAt: new Date(),
    gift: {
      id: 'gift1',
      name: 'Digital Gift Card',
      description: 'A digital gift card for recipient',
      imageUrl: '',
      price: 50,
      category: 'digital',
      isAvailable: true
    }
  },
  {
    id: '2',
    message: 'We wanted to express our gratitude for your partnership. Looking forward to our continued collaboration.',
    senderId: 'user3',
    senderCompanyId: 'company3',
    senderCompany: { 
      id: 'company3', 
      name: 'GlobalBiz', 
      logoUrl: '', 
      createdAt: new Date() 
    },
    recipientId: 'user2',
    recipientCompanyId: 'company2',
    recipientCompany: { 
      id: 'company2', 
      name: 'AcmeCo', 
      logoUrl: '', 
      createdAt: new Date() 
    },
    status: 'viewed',
    createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
    gift: null
  },
  {
    id: '3',
    message: 'Your presentation last week was inspirational. We\'ve implemented several of your ideas already.',
    senderId: 'user4',
    senderCompanyId: 'company4',
    senderCompany: { 
      id: 'company4', 
      name: 'FutureTech', 
      logoUrl: '', 
      createdAt: new Date() 
    },
    recipientId: 'user2',
    recipientCompanyId: 'company2',
    recipientCompany: { 
      id: 'company2', 
      name: 'AcmeCo', 
      logoUrl: '', 
      createdAt: new Date() 
    },
    status: 'responded',
    createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
    gift: {
      id: 'gift2',
      name: 'Donation',
      description: 'A donation to a cause',
      imageUrl: '',
      price: 25,
      category: 'donation',
      isAvailable: true
    }
  },
];

export function Inbox() {
  const [searchTerm, setSearchTerm] = useState('');
  
  const filteredFlawagrams = receivedFlawagrams.filter(flawagram => 
    flawagram.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
    flawagram.senderCompany.name.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">Inbox</h1>
        <p className="text-gray-500">View and respond to Flawagrams you've received.</p>
      </div>
      
      <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
        <div className="relative w-full md:w-auto flex-1 max-w-sm">
          <SearchIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search Flawagrams..."
            className="pl-9"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        
        <div className="flex gap-2 w-full md:w-auto">
          <Button variant="outline" className="flex-1 md:flex-initial">
            <FilterIcon className="mr-2 h-4 w-4" />
            Filter
          </Button>
          <select className="h-10 rounded-md border border-gray-200 bg-white px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-black focus-visible:ring-offset-2">
            <option value="recent">Most Recent</option>
            <option value="oldest">Oldest First</option>
            <option value="unresponded">Unresponded</option>
          </select>
        </div>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle className="text-base font-medium">
            Your Received Flawagrams
          </CardTitle>
        </CardHeader>
        <CardContent>
          {filteredFlawagrams.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {filteredFlawagrams.map(flawagram => (
                <FlawagramCard
                  key={flawagram.id}
                  flawagram={flawagram}
                  type="received"
                  onView={(id) => console.log('View flawagram', id)}
                  onRespond={(id) => console.log('Respond to flawagram', id)}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-500">No Flawagrams found matching your search.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}