import React, { createContext, useContext, useState, useEffect } from 'react';
import { User, Company } from '../types';

interface AuthContextType {
  user: User | null;
  company: Company | null;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (companyName: string, email: string, password: string) => Promise<void>;
  logout: () => void;
  updateUser: (user: User) => void;
  updateCompany: (company: Company) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Mock data for demonstration
const mockCompanies: Company[] = [
  {
    id: 'company1',
    name: 'TechCorp Solutions',
    logoUrl: '',
    industry: 'Technology',
    size: '50-100',
    createdAt: new Date('2024-01-15'),
  },
  {
    id: 'company2',
    name: 'AcmeCo Industries',
    logoUrl: '',
    industry: 'Manufacturing',
    size: '100-500',
    createdAt: new Date('2024-02-01'),
  },
];

const mockUsers: User[] = [
  {
    id: 'user1',
    name: 'John Smith',
    email: '<EMAIL>',
    role: 'admin',
    companyId: 'company1',
  },
  {
    id: 'user2',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'admin',
    companyId: 'company2',
  },
];

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [company, setCompany] = useState<Company | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check for existing session
    const savedUser = localStorage.getItem('flawagram_user');
    const savedCompany = localStorage.getItem('flawagram_company');
    
    if (savedUser && savedCompany) {
      setUser(JSON.parse(savedUser));
      setCompany(JSON.parse(savedCompany));
    }
    
    setIsLoading(false);
  }, []);

  const login = async (email: string, password: string) => {
    setIsLoading(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock authentication
    const foundUser = mockUsers.find(u => u.email === email);
    if (!foundUser) {
      throw new Error('Invalid email or password');
    }
    
    const foundCompany = mockCompanies.find(c => c.id === foundUser.companyId);
    if (!foundCompany) {
      throw new Error('Company not found');
    }
    
    setUser(foundUser);
    setCompany(foundCompany);
    
    // Save to localStorage
    localStorage.setItem('flawagram_user', JSON.stringify(foundUser));
    localStorage.setItem('flawagram_company', JSON.stringify(foundCompany));
    
    setIsLoading(false);
  };

  const register = async (companyName: string, email: string, password: string) => {
    setIsLoading(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // Create new company and user
    const newCompany: Company = {
      id: `company_${Date.now()}`,
      name: companyName,
      logoUrl: '',
      createdAt: new Date(),
    };
    
    const newUser: User = {
      id: `user_${Date.now()}`,
      name: email.split('@')[0], // Extract name from email for demo
      email,
      role: 'admin',
      companyId: newCompany.id,
    };
    
    setUser(newUser);
    setCompany(newCompany);
    
    // Save to localStorage
    localStorage.setItem('flawagram_user', JSON.stringify(newUser));
    localStorage.setItem('flawagram_company', JSON.stringify(newCompany));
    
    setIsLoading(false);
  };

  const logout = () => {
    setUser(null);
    setCompany(null);
    localStorage.removeItem('flawagram_user');
    localStorage.removeItem('flawagram_company');
  };

  const updateUser = (updatedUser: User) => {
    setUser(updatedUser);
    localStorage.setItem('flawagram_user', JSON.stringify(updatedUser));
  };

  const updateCompany = (updatedCompany: Company) => {
    setCompany(updatedCompany);
    localStorage.setItem('flawagram_company', JSON.stringify(updatedCompany));
  };

  const value = {
    user,
    company,
    isLoading,
    login,
    register,
    logout,
    updateUser,
    updateCompany,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}
