import React from 'react';
import { <PERSON><PERSON> } from '../components/ui/Button';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '../components/ui/Card';
import { Users, Target, Award, Heart } from 'lucide-react';

export function AboutPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 sm:text-5xl md:text-6xl">
              About <span className="text-blue-600">Flawa</span>
            </h1>
            <p className="mt-6 max-w-3xl mx-auto text-xl text-gray-500">
              We're revolutionizing workplace communication by making feedback more 
              engaging, actionable, and meaningful through our innovative Flawagram platform.
            </p>
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="lg:grid lg:grid-cols-2 lg:gap-8 lg:items-center">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">
                Our Mission
              </h2>
              <p className="mt-4 text-lg text-gray-500">
                To transform how teams communicate by creating a platform where feedback 
                flows naturally, recognition is meaningful, and every voice is heard. 
                We believe that great communication is the foundation of great teams.
              </p>
              <p className="mt-4 text-lg text-gray-500">
                Flawa makes it easy to send structured, actionable feedback that helps 
                individuals and teams grow, while building a culture of continuous 
                improvement and mutual support.
              </p>
            </div>
            <div className="mt-8 lg:mt-0">
              <div className="bg-blue-600 rounded-lg p-8 text-white">
                <h3 className="text-2xl font-bold">Why Flawagrams?</h3>
                <ul className="mt-4 space-y-3">
                  <li className="flex items-start">
                    <span className="flex-shrink-0 h-6 w-6 text-blue-200">•</span>
                    <span className="ml-3">Structured feedback that's easy to understand</span>
                  </li>
                  <li className="flex items-start">
                    <span className="flex-shrink-0 h-6 w-6 text-blue-200">•</span>
                    <span className="ml-3">Anonymous options for honest communication</span>
                  </li>
                  <li className="flex items-start">
                    <span className="flex-shrink-0 h-6 w-6 text-blue-200">•</span>
                    <span className="ml-3">Team-wide visibility and transparency</span>
                  </li>
                  <li className="flex items-start">
                    <span className="flex-shrink-0 h-6 w-6 text-blue-200">•</span>
                    <span className="ml-3">Actionable insights for continuous improvement</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="bg-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">
              Our Values
            </h2>
            <p className="mt-4 text-lg text-gray-500">
              The principles that guide everything we do
            </p>
          </div>
          
          <div className="mt-12 grid gap-8 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="text-center">
                <Users className="h-12 w-12 text-blue-600 mx-auto" />
                <CardTitle className="mt-4">Collaboration</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-500 text-center">
                  We believe the best solutions come from teams working together, 
                  sharing ideas, and building on each other's strengths.
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="text-center">
                <Target className="h-12 w-12 text-blue-600 mx-auto" />
                <CardTitle className="mt-4">Purpose</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-500 text-center">
                  Every feature we build has a clear purpose: to make workplace 
                  communication more effective and meaningful.
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="text-center">
                <Award className="h-12 w-12 text-blue-600 mx-auto" />
                <CardTitle className="mt-4">Excellence</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-500 text-center">
                  We're committed to delivering a platform that exceeds expectations 
                  and helps teams achieve their best work.
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="text-center">
                <Heart className="h-12 w-12 text-blue-600 mx-auto" />
                <CardTitle className="mt-4">Empathy</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-500 text-center">
                  We understand that feedback can be sensitive, and we design 
                  with care for both the giver and receiver.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">
              Built by a Team That Cares
            </h2>
            <p className="mt-4 text-lg text-gray-500 max-w-3xl mx-auto">
              Our diverse team of designers, developers, and workplace communication 
              experts is passionate about creating tools that make work more human 
              and more effective.
            </p>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-blue-600 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white sm:text-4xl">
            Ready to Transform Your Team Communication?
          </h2>
          <p className="mt-4 text-xl text-blue-100">
            Join thousands of teams already using Flawa to build better workplace relationships.
          </p>
          <div className="mt-8">
            <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
              Get Started Today
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
