import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Button } from '../../components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/Card';
import { DashboardStats, getDefaultStats } from '../../components/dashboard/DashboardStats';
import { FlawagramCard } from '../../components/dashboard/FlawagramCard';
import { SendIcon, TrendingUpIcon } from 'lucide-react';

// Mock data
const recentFlawagrams = [
  {
    id: '1',
    message: 'Thank you for your excellent work on the recent project. Your team\'s attention to detail was outstanding!',
    senderId: 'user1',
    senderCompanyId: 'company1',
    senderCompany: { 
      id: 'company1', 
      name: 'TechCorp', 
      logoUrl: '', 
      createdAt: new Date() 
    },
    recipientId: 'user2',
    recipientCompanyId: 'company2',
    recipientCompany: { 
      id: 'company2', 
      name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', 
      logoUrl: '', 
      createdAt: new Date() 
    },
    status: 'delivered',
    createdAt: new Date(),
    gift: {
      id: 'gift1',
      name: 'Digital Gift Card',
      description: 'A digital gift card for recipient',
      imageUrl: '',
      price: 50,
      category: 'digital',
      isAvailable: true
    }
  },
  {
    id: '2',
    message: 'We wanted to express our gratitude for your partnership. Looking forward to our continued collaboration.',
    senderId: 'user3',
    senderCompanyId: 'company3',
    senderCompany: { 
      id: 'company3', 
      name: 'GlobalBiz', 
      logoUrl: '', 
      createdAt: new Date() 
    },
    recipientId: 'user2',
    recipientCompanyId: 'company2',
    recipientCompany: { 
      id: 'company2', 
      name: 'AcmeCo', 
      logoUrl: '', 
      createdAt: new Date() 
    },
    status: 'viewed',
    createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
    gift: null
  },
];

export function DashboardHome() {
  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-gray-500">Welcome back to your Flawagram dashboard.</p>
        </div>
        <Link to="/dashboard/send">
          <Button>
            <SendIcon className="mr-2 h-4 w-4" />
            Send Flawagram
          </Button>
        </Link>
      </div>
      
      <DashboardStats stats={getDefaultStats()} />
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div className="md:col-span-2">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-base font-medium">
                Recent Flawagrams Received
              </CardTitle>
              <Link to="/dashboard/inbox" className="text-xs text-gray-500 hover:underline">
                View all
              </Link>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentFlawagrams.map(flawagram => (
                  <FlawagramCard
                    key={flawagram.id}
                    flawagram={flawagram}
                    type="received"
                    onView={(id) => console.log('View flawagram', id)}
                    onRespond={(id) => console.log('Respond to flawagram', id)}
                  />
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
        
        <div>
          <Card className="h-full">
            <CardHeader>
              <CardTitle className="text-base font-medium">
                Activity Summary
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-black"></div>
                  <p className="text-sm">8 Flawagrams sent this month</p>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-black"></div>
                  <p className="text-sm">12 Flawagrams received</p>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-green-600"></div>
                  <p className="text-sm flex items-center">
                    Response rate up 5%
                    <TrendingUpIcon className="h-3 w-3 ml-1 text-green-600" />
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-black"></div>
                  <p className="text-sm">3 Team members added</p>
                </div>
                
                <hr className="my-2" />
                
                <div className="pt-2">
                  <p className="text-sm font-medium mb-2">Quick Actions</p>
                  <div className="space-y-2">
                    <Link to="/dashboard/send">
                      <Button variant="outline" size="sm" className="w-full justify-start">
                        <SendIcon className="mr-2 h-4 w-4" />
                        Send a Flawagram
                      </Button>
                    </Link>
                    <Link to="/dashboard/team">
                      <Button variant="outline" size="sm" className="w-full justify-start">
                        <SendIcon className="mr-2 h-4 w-4" />
                        Invite Team Member
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}