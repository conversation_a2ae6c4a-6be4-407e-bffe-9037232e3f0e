import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';

// Layouts
import { DashboardLayout } from './components/layouts/DashboardLayout';
import { AuthLayout } from './components/layouts/AuthLayout';
import { MarketingLayout } from './components/layouts/MarketingLayout';

// Pages
import { HomePage } from './pages/HomePage';
import { LoginPage } from './pages/auth/LoginPage';
import { RegisterPage } from './pages/auth/RegisterPage';
import { DashboardHome } from './pages/dashboard/DashboardHome';
import { SendFlawagram } from './pages/dashboard/SendFlawagram';
import { Inbox } from './pages/dashboard/Inbox';
import { Outbox } from './pages/dashboard/Outbox';
import { TeamManagement } from './pages/dashboard/TeamManagement';
import { SettingsPage } from './pages/dashboard/SettingsPage';
import { FlawagramPage } from './pages/FlawagramPage';
import { AboutPage } from './pages/AboutPage';
import { HowItWorksPage } from './pages/HowItWorksPage';
import { PricingPage } from './pages/PricingPage';
import { NotFoundPage } from './pages/NotFoundPage';

function App() {
  return (
    <Router>
      <Routes>
        {/* Public Marketing Pages */}
        <Route element={<MarketingLayout />}>
          <Route path="/" element={<HomePage />} />
          <Route path="/about" element={<AboutPage />} />
          <Route path="/how-it-works" element={<HowItWorksPage />} />
          <Route path="/pricing" element={<PricingPage />} />
        </Route>
        
        {/* Public Flawagram View Page */}
        <Route path="/flawagram/:id" element={<FlawagramPage />} />
        
        {/* Auth Pages */}
        <Route element={<AuthLayout />}>
          <Route path="/login" element={<LoginPage />} />
          <Route path="/register" element={<RegisterPage />} />
        </Route>
        
        {/* Dashboard Pages */}
        <Route element={<DashboardLayout />}>
          <Route path="/dashboard" element={<DashboardHome />} />
          <Route path="/dashboard/send" element={<SendFlawagram />} />
          <Route path="/dashboard/inbox" element={<Inbox />} />
          <Route path="/dashboard/outbox" element={<Outbox />} />
          <Route path="/dashboard/team" element={<TeamManagement />} />
          <Route path="/dashboard/settings" element={<SettingsPage />} />
        </Route>
        
        {/* 404 Page */}
        <Route path="*" element={<NotFoundPage />} />
      </Routes>
    </Router>
  );
}

export default App;